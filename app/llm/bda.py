
import boto3
import time

class SimpleBDAProcessor:
    """
    Simple class to start BDA processing for a file already in S3 and fetch results.
    No logging, minimal logic.
    """
    def __init__(self, region: str, project_id: str):
        self.bda_client = boto3.client('bedrock-data-automation-runtime', region_name=region)
        self.sts_client = boto3.client('sts')
        self.project_id = project_id
        self.aws_account_id = self.sts_client.get_caller_identity().get('Account')

    def start_processing(self, input_s3_uri: str, output_s3_uri: str) -> str:
        """
        Start BDA processing for a file in S3. Returns invocation ARN.
        """
        profile_arn = f"arn:aws:bedrock:us-east-1:************:data-automation-profile/us.data-automation-v1"
        response = self.bda_client.invoke_data_automation_async(
            inputConfiguration={'s3Uri': input_s3_uri},
            outputConfiguration={'s3Uri': output_s3_uri},
            dataAutomationProfileArn=profile_arn,
            blueprints=[
                {
                    "blueprintArn": "arn:aws:bedrock:us-east-1:aws:blueprint/bedrock-data-automation-public-invoice"
                }
            ]
        )
        return response.get('invocationArn')

    def get_result(self, invocation_arn: str) -> dict:
        """
        Polls for job status and returns result dict when available.
        """
        while True:
            status_response = self.bda_client.get_data_automation_status(invocationArn=invocation_arn)
            status = status_response.get('status')
            if status == 'Success':
                return status_response
            elif status == 'Failed':
                return status_response
            time.sleep(10)


def main():
    # Example usage
    region = 'us-east-1'
    project_id = 'ad4a57a0c572'  # Replace with your project ID
    input_s3_uri = 's3://document-extraction-logistically/temp/11173426_carrier_invoice.pdf'  # Replace with your S3 input file
    output_s3_uri = 's3://document-extraction-logistically/temp/output/'  # Replace with your S3 output folder

    processor = SimpleBDAProcessor(region, project_id)
    invocation_arn = processor.start_processing(input_s3_uri, output_s3_uri)
    print(f"Started processing. Invocation ARN: {invocation_arn}")

    result = processor.get_result(invocation_arn)
    print("Processing result:")
    print(result)


if __name__ == "__main__":
    main()


