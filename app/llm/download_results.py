#!/usr/bin/env python3
"""
Download and display BDA processing results
"""

import boto3
import os
import json
from botocore.exceptions import ClientError

def download_from_s3(bucket, s3_file, local_file):
    """Download file from S3 with error handling"""
    s3 = boto3.client('s3', region_name='us-east-1')
    try:
        # Ensure local directory exists
        os.makedirs(os.path.dirname(local_file), exist_ok=True)
        
        # Download the file
        s3.download_file(bucket, s3_file, local_file)
        print(f"✅ Downloaded: {s3_file}")
        return local_file
    except ClientError as e:
        print(f"❌ Error downloading {s3_file}: {e}")
        return None

def extract_s3_path(s3_uri):
    """Extract bucket and key from S3 URI"""
    if s3_uri.startswith('s3://'):
        parts = s3_uri[5:].split('/', 1)
        bucket = parts[0]
        key = parts[1] if len(parts) > 1 else ''
        return bucket, key
    return None, None

def main():
    # Download job metadata first
    bucket = 'document-extraction-logistically'
    metadata_s3_file = 'temp/output//18748b53-2269-4f31-b32a-7056118b9c1a/job_metadata.json'
    metadata_local_file = '/tmp/job_metadata.json'
    
    print("📥 Downloading job metadata...")
    metadata_file = download_from_s3(bucket, metadata_s3_file, metadata_local_file)
    
    if not metadata_file:
        print("❌ Failed to download metadata")
        return
    
    # Parse metadata to get actual results path
    with open(metadata_file, 'r') as f:
        metadata = json.load(f)
    
    print("\n📋 Job Metadata:")
    print(f"Job ID: {metadata['job_id']}")
    print(f"Status: {metadata['job_status']}")
    print(f"Semantic Modality: {metadata['semantic_modality']}")
    
    # Extract custom output path
    if 'output_metadata' in metadata and metadata['output_metadata']:
        asset_metadata = metadata['output_metadata'][0]
        if 'segment_metadata' in asset_metadata and asset_metadata['segment_metadata']:
            segment = asset_metadata['segment_metadata'][0]
            custom_output_path = segment.get('custom_output_path')
            
            if custom_output_path:
                print(f"\n🎯 Found extracted data at: {custom_output_path}")
                
                # Download the actual extracted data
                result_bucket, result_key = extract_s3_path(custom_output_path)
                result_local_file = '/tmp/extraction_result.json'
                
                print("📥 Downloading extraction results...")
                result_file = download_from_s3(result_bucket, result_key, result_local_file)
                
                if result_file:
                    print("\n📄 Extracted Data:")
                    print("=" * 80)
                    with open(result_file, 'r') as f:
                        result_content = f.read()
                        try:
                            result_json = json.loads(result_content)
                            print(json.dumps(result_json, indent=2))
                        except json.JSONDecodeError:
                            print(result_content)
                    
                    print("\n" + "=" * 80)
                    print("✅ Processing complete! Both metadata and extracted data downloaded successfully.")
                else:
                    print("❌ Failed to download extraction results")
            else:
                print("❌ No custom output path found in metadata")
    else:
        print("❌ No output metadata found")

if __name__ == "__main__":
    main()
