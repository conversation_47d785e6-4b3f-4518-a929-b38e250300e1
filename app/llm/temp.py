import boto3
import os
from botocore.exceptions import NoCredentialsError, ClientError

def upload_to_aws(local_file, bucket, s3_file):

    s3 = boto3.client('s3', region_name='us-east-1')

    s3.upload_file(local_file, bucket, s3_file)
    print("Upload Successful")
    # Construct and return the S3 URI
    s3_uri = f"s3://{bucket}/{s3_file}"
    return s3_uri

local_file = r'/home/<USER>/Documents/repositories/logistically/data/input_data/extraction/processed/11173426_carrier_invoice.pdf'
bucket = 'document-extraction-logistically'
s3_file = 'temp/11173426_carrier_invoice.pdf'

result = upload_to_aws(local_file, bucket, s3_file)

if result:
    print(f"File uploaded to: {result}")
else:
    print("Upload failed")