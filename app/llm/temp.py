import boto3
import os
from botocore.exceptions import NoCredentialsError, ClientError
from bedrock_data_automation import SimpleBDAProcessor

def upload_to_aws(local_file, bucket, s3_file):
    s3 = boto3.client('s3', region_name='us-east-1')
    s3.upload_file(local_file, bucket, s3_file)
    print("Upload Successful")
    # Construct and return the S3 URI
    s3_uri = f"s3://{bucket}/{s3_file}"
    return s3_uri

def process_with_bda(input_s3_uri, output_s3_uri, project_id='ad4a57a0c572'):
    """Process a file using SimpleBDAProcessor"""
    try:
        print(f"Initializing SimpleBDAProcessor...")
        processor = SimpleBDAProcessor('us-east-1', project_id)

        print(f"Starting BDA processing...")
        print(f"Input: {input_s3_uri}")
        print(f"Output: {output_s3_uri}")

        # Start processing
        invocation_arn = processor.start_processing(input_s3_uri, output_s3_uri)
        print(f"Processing started with ARN: {invocation_arn}")

        # Get results (this will poll until completion)
        print("Waiting for processing to complete...")
        result = processor.get_result(invocation_arn)

        print("Processing completed!")
        print(f"Status: {result.get('status')}")

        if result.get('status') == 'Success':
            print("✅ Processing successful!")
            output_s3_uri = result.get('outputS3Uri') or result.get('outputConfiguration', {}).get('s3Uri')
            if output_s3_uri:
                print(f"Results available at: {output_s3_uri}")
        else:
            print("❌ Processing failed!")
            print(f"Error details: {result}")

        return result

    except Exception as e:
        print(f"Error during BDA processing: {e}")
        return None

# Configuration
local_file = r'/home/<USER>/Documents/repositories/logistically/data/input_data/extraction/processed/11173426_carrier_invoice.pdf'
bucket = 'document-extraction-logistically'
s3_file = 'temp/11173426_carrier_invoice.pdf'

# Step 1: Upload file to S3 (if needed)
print("=== Step 1: Upload to S3 ===")
input_s3_uri = upload_to_aws(local_file, bucket, s3_file)

if input_s3_uri:
    print(f"File uploaded to: {input_s3_uri}")

    # Step 2: Process with SimpleBDAProcessor
    print("\n=== Step 2: Process with BDA ===")
    output_s3_uri = f"s3://{bucket}/temp/output/"

    result = process_with_bda(input_s3_uri, output_s3_uri)

    if result:
        print("\n=== Processing Summary ===")
        print(f"Input file: {input_s3_uri}")
        print(f"Output location: {output_s3_uri}")
        print(f"Final status: {result.get('status')}")
    else:
        print("Processing failed!")
else:
    print("Upload failed - cannot proceed with BDA processing")