# code to download s3 file and print in console

import boto3
import os
import json
from botocore.exceptions import NoCredentialsError, ClientError

def list_s3_objects(bucket, prefix):
    """List objects in S3 bucket with given prefix"""
    s3 = boto3.client('s3', region_name='us-east-1')
    try:
        response = s3.list_objects_v2(Bucket=bucket, Prefix=prefix)
        if 'Contents' in response:
            return [obj['Key'] for obj in response['Contents']]
        else:
            return []
    except ClientError as e:
        print(f"Error listing S3 objects: {e}")
        return []

def download_from_s3(bucket, s3_file, local_file):
    """Download file from S3 with error handling"""
    s3 = boto3.client('s3', region_name='us-east-1')
    try:
        # Check if file exists first
        s3.head_object(Bucket=bucket, Key=s3_file)

        # Ensure local directory exists
        os.makedirs(os.path.dirname(local_file), exist_ok=True)

        # Download the file
        s3.download_file(bucket, s3_file, local_file)
        print(f"✅ Download Successful: {s3_file}")
        return local_file
    except ClientError as e:
        error_code = e.response['Error']['Code']
        if error_code == '404':
            print(f"❌ File not found: s3://{bucket}/{s3_file}")

            # Try to find similar files
            print("🔍 Looking for similar files...")
            prefix = '/'.join(s3_file.split('/')[:-1])  # Get directory path
            objects = list_s3_objects(bucket, prefix)

            if objects:
                print(f"Found {len(objects)} objects with prefix '{prefix}':")
                for obj in objects[:10]:  # Show first 10
                    print(f"  - {obj}")
                if len(objects) > 10:
                    print(f"  ... and {len(objects) - 10} more")
            else:
                print(f"No objects found with prefix '{prefix}'")

                # Try broader search
                broader_prefix = '/'.join(s3_file.split('/')[:-2])
                print(f"🔍 Trying broader search with prefix '{broader_prefix}'...")
                broader_objects = list_s3_objects(bucket, broader_prefix)
                if broader_objects:
                    print(f"Found {len(broader_objects)} objects:")
                    for obj in broader_objects[:10]:
                        print(f"  - {obj}")
        else:
            print(f"❌ Error downloading file: {e}")
        return None

def main():
    bucket = 'document-extraction-logistically'

    # Try the corrected path with double slash
    s3_file = 'temp/output//18748b53-2269-4f31-b32a-7056118b9c1a/job_metadata.json'
    local_file = '/tmp/job_metadata.json'

    print(f"Attempting to download: s3://{bucket}/{s3_file}")

    result = download_from_s3(bucket, s3_file, local_file)

    if result:
        print("\n📄 File contents:")
        print("=" * 50)
        try:
            with open(local_file, 'r') as f:
                content = f.read()
                # Try to pretty print if it's JSON
                try:
                    json_content = json.loads(content)
                    print(json.dumps(json_content, indent=2))
                except json.JSONDecodeError:
                    print(content)
        except Exception as e:
            print(f"Error reading file: {e}")
    else:
        print("\n❌ Download failed!")

if __name__ == "__main__":
    main()