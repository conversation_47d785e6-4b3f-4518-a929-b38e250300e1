"""
Worker to process S3 events from an SQS queue.
"""
import json
import time
import boto3
from botocore.exceptions import ClientError
from loguru import logger
import os

from app.core.configuration import settings
from app.services.s3_service import s3_service
# NOTE: You would import your ingestion service here once it's created.
# from app.services.ingestion_service import process_document


class S3EventProcessor:
    """
    A worker that processes S3 upload events from an SQS queue.
    """
    def __init__(self, queue_url: str, region_name: str):
        if not queue_url:
            raise ValueError("SQS_QUEUE_URL must be configured in your .env file to run the worker.")
        
        self.sqs_client = boto3.client(
            "sqs",
            aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
            aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY,
            aws_session_token=settings.AWS_SESSION_TOKEN,
            region_name=region_name
        )
        self.queue_url = queue_url
        logger.info(f"Worker initialized for SQS queue: {queue_url}")

    def process_messages(self):
        """
        Continuously poll the SQS queue for messages and process them.
        """
        logger.info("Starting S3 event processor worker...")
        while True:
            try:
                # Use long-polling to efficiently wait for messages
                response = self.sqs_client.receive_message(
                    QueueUrl=self.queue_url,
                    MaxNumberOfMessages=10,  # Process up to 10 messages at a time
                    WaitTimeSeconds=20,      # Enable long polling
                    MessageAttributeNames=['All']
                )

                messages = response.get('Messages', [])
                if not messages:
                    logger.debug("No new messages. Waiting...")
                    continue

                for message in messages:
                    receipt_handle = message['ReceiptHandle']
                    try:
                        logger.info(f"Received message: {message['MessageId']}")
                        body = json.loads(message['Body'])
                        
                        # S3 event messages might be wrapped in an SNS format
                        s3_event = json.loads(body['Message']) if 'Message' in body else body

                        if 'Records' not in s3_event:
                            logger.warning("Message is not a valid S3 event. Skipping.")
                            self._delete_message(receipt_handle)
                            continue

                        for record in s3_event['Records']:
                            bucket_name = record['s3']['bucket']['name']
                            object_key = record['s3']['object']['key']
                            
                            logger.info(f"Processing file: s3://{bucket_name}/{object_key}")
                            
                            # This is where you would call your ingestion logic
                            self._handle_ingestion(bucket_name, object_key)

                        # Delete the message from the queue after successful processing
                        self._delete_message(receipt_handle)

                    except Exception as e:
                        logger.error(f"Error processing message {message['MessageId']}: {e}")
                        # Do not delete the message, let it be re-processed after visibility timeout

            except ClientError as e:
                logger.error(f"SQS client error: {e}")
                time.sleep(10) # Wait before retrying

    def _handle_ingestion(self, bucket_name: str, object_key: str):
        """
        Downloads the file and triggers the ingestion process.
        The logic from `app/api/endpoints/ingestion.py` should be moved to a service that is called here.
        """
        temp_file = None
        try:
            temp_file = s3_service.download_file(bucket_name, object_key)
            # Example: process_document(temp_file.name, file_metadata={...})
            logger.success(f"Successfully ingested s3://{bucket_name}/{object_key}")
        finally:
            if temp_file:
                # Clean up the temporary file
                os.unlink(temp_file.name)

    def _delete_message(self, receipt_handle: str):
        """Delete a message from the SQS queue."""
        try:
            self.sqs_client.delete_message(QueueUrl=self.queue_url, ReceiptHandle=receipt_handle)
            logger.info(f"Message with handle {receipt_handle[:10]}... deleted.")
        except ClientError as e:
            logger.error(f"Failed to delete message: {e}")


if __name__ == "__main__":
    # This allows running the worker as a standalone script.
    # In your terminal, run: python -m app.workers.s3_event_processor
    worker = S3EventProcessor(queue_url=settings.SQS_QUEUE_URL, region_name=settings.AWS_REGION)
    worker.process_messages()